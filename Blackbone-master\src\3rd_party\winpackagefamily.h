/*

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    winpackagefamily.h

Abstract:

    API family partitioning based on packages.

*/

#ifndef _INC_WINPACKAGEFAMILY
#define _INC_WINPACKAGEFAMILY

#if defined(_MSC_VER) && !defined(MO<PERSON>OMP_PASS)
#if _MSC_VER >= 1200
#pragma warning(push)
#pragma warning(disable:4001) /* nonstandard extension 'single line comment' was used */
#endif
#pragma once
#endif // defined(_MSC_VER) && !defined(MOFCOMP_PASS)

#ifndef WINAPI_PARTITION_SERVER
#define WINAPI_PARTITION_SERVER (WINAPI_FAMILY == WINAPI_FAMILY_SERVER)
#endif

/*
 * PARTITIONS based on packages are each #undef'ed below, and then will be #define-ed
 * to be either 1 or 0 or depending on the active WINAPI_FAMILY.
 */
#undef WINAPI_PARTITION_PKG_WINTRUST
#undef WINAPI_PARTITION_PKG_WEBSERVICES
#undef WINAPI_PARTITION_PKG_EVENTLOGSERVICE
#undef WINAPI_PARTITION_PKG_VHD
#undef WINAPI_PARTITION_PKG_PERFCOUNTER
#undef WINAPI_PARTITION_PKG_SECURESTARTUP
#undef WINAPI_PARTITION_PKG_REMOTEFS
#undef WINAPI_PARTITION_PKG_BOOTABLESKU
#undef WINAPI_PARTITION_PKG_CMDTOOLS
#undef WINAPI_PARTITION_PKG_DISM
#undef WINAPI_PARTITION_PKG_CORESETUP
#undef WINAPI_PARTITION_PKG_APPRUNTIME
#undef WINAPI_PARTITION_PKG_ESENT
#undef WINAPI_PARTITION_PKG_WINMGMT
#undef WINAPI_PARTITION_PKG_WNV
#undef WINAPI_PARTITION_PKG_CLUSTER
#undef WINAPI_PARTITION_PKG_VSS
#undef WINAPI_PARTITION_PKG_TRAFFIC
#undef WINAPI_PARTITION_PKG_ISCSI
#undef WINAPI_PARTITION_PKG_STORAGE
#undef WINAPI_PARTITION_PKG_MPSSVC
#undef WINAPI_PARTITION_PKG_APPXDEPLOYMENT
#undef WINAPI_PARTITION_PKG_WER

/* 
 * PARTITIONS for feature packages. Each package might be active for one or more editions
 */
#define WINAPI_PARTITION_PKG_WINTRUST         (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_WEBSERVICES      (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_EVENTLOGSERVICE  (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_VHD              (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_PERFCOUNTER      (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_SECURESTARTUP    (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_REMOTEFS         (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_BOOTABLESKU      (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_CMDTOOLS         (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_DISM             (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_CORESETUP        (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_APPRUNTIME       (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_ESENT            (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_WINMGMT          (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_WNV              (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_CLUSTER          (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_VSS              (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_TRAFFIC          (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_ISCSI            (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_STORAGE          (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_MPSSVC           (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_APPXDEPLOYMENT   (WINAPI_PARTITION_SERVER == 1)
#define WINAPI_PARTITION_PKG_WER              (WINAPI_PARTITION_SERVER == 1)

#if defined(_MSC_VER) && !defined(MOFCOMP_PASS)
#if _MSC_VER >= 1200
#pragma warning(pop)
#endif
#endif

#endif  /* !_INC_WINPACKAGEFAMILY */
