﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Win10Debug|x64">
      <Configuration>Win10Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win10Release|x64">
      <Configuration>Win10Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win8.1 Debug|x64">
      <Configuration>Win8.1 Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win8 Debug|x64">
      <Configuration>Win8 Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win8.1 Release|x64">
      <Configuration>Win8.1 Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win8 Release|x64">
      <Configuration>Win8 Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win7 Debug|x64">
      <Configuration>Win7 Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win7 Release|x64">
      <Configuration>Win7 Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{50429DDC-D8EC-4926-B913-D7ADE6A3DC9F}</ProjectGuid>
    <TemplateGuid>{dd38f7fc-d7bd-488b-9242-7d8754cde80d}</TemplateGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <MinimumVisualStudioVersion>11.0</MinimumVisualStudioVersion>
    <Configuration>Win8 Debug</Configuration>
    <Platform Condition="'$(Platform)' == ''">Win32</Platform>
  </PropertyGroup>
  <PropertyGroup Label="Globals">
    <ProjectName>BlackBoneDrv</ProjectName>
  </PropertyGroup>
  <PropertyGroup Label="PropertySheets">
    <ConfigurationType>Driver</ConfigurationType>
    <DriverType>WDM</DriverType>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Win8.1 Debug|x64'">
    <TargetVersion>WindowsV6.3</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <SupportsPackaging>false</SupportsPackaging>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win10Debug|x64'" Label="Configuration">
    <TargetVersion>
    </TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <SupportsPackaging>false</SupportsPackaging>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win8 Debug|x64'" Label="Configuration">
    <TargetVersion>Windows8</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <SupportsPackaging>false</SupportsPackaging>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Win8.1 Release|x64'">
    <TargetVersion>WindowsV6.3</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <SupportsPackaging>false</SupportsPackaging>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win10Release|x64'" Label="Configuration">
    <TargetVersion>
    </TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <SupportsPackaging>false</SupportsPackaging>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win8 Release|x64'" Label="Configuration">
    <TargetVersion>Windows8</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <SupportsPackaging>false</SupportsPackaging>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win7 Debug|x64'" Label="Configuration">
    <TargetVersion>Windows7</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <SupportsPackaging>false</SupportsPackaging>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win7 Release|x64'" Label="Configuration">
    <TargetVersion>Windows7</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <SupportsPackaging>false</SupportsPackaging>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win8.1 Debug|x64'">
    <OutDir>$(SolutionDir)bin\$(Platform)\$(ConfigurationName)\</OutDir>
    <TimeStampServer>http://timestamp.verisign.com/scripts/timstamp.dll</TimeStampServer>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(ConfigurationName)\</IntDir>
    <TargetName>$(TargetName)81</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win10Debug|x64'">
    <OutDir>$(SolutionDir)bin\$(Platform)\$(ConfigurationName)\</OutDir>
    <TimeStampServer>http://timestamp.verisign.com/scripts/timstamp.dll</TimeStampServer>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(ConfigurationName)\</IntDir>
    <TargetName>$(TargetName)10</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win8 Debug|x64'">
    <OutDir>$(SolutionDir)bin\$(Platform)\$(ConfigurationName)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(ConfigurationName)\</IntDir>
    <TargetName>$(TargetName)8</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win7 Debug|x64'">
    <OutDir>$(SolutionDir)bin\$(Platform)\$(ConfigurationName)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(ConfigurationName)\</IntDir>
    <TargetName>$(TargetName)7</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win7 Release|x64'">
    <OutDir>$(SolutionDir)bin\$(Platform)\$(ConfigurationName)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(ConfigurationName)\</IntDir>
    <TargetName>$(TargetName)7</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win8.1 Release|x64'">
    <OutDir>$(SolutionDir)bin\$(Platform)\$(ConfigurationName)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(ConfigurationName)\</IntDir>
    <TargetName>$(TargetName)81</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win10Release|x64'">
    <OutDir>$(SolutionDir)bin\$(Platform)\$(ConfigurationName)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(ConfigurationName)\</IntDir>
    <TargetName>$(TargetName)10</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win8 Release|x64'">
    <OutDir>$(SolutionDir)bin\$(Platform)\$(ConfigurationName)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(ConfigurationName)\</IntDir>
    <TargetName>$(TargetName)8</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win8.1 Debug|x64'">
    <ClCompile>
      <CompileAs>CompileAsC</CompileAs>
      <AdditionalIncludeDirectories>
      </AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WIN81_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Link>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win10Debug|x64'">
    <ClCompile>
      <CompileAs>Default</CompileAs>
      <AdditionalIncludeDirectories>
      </AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WIN10_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Link>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win8 Debug|x64'">
    <ClCompile>
      <CompileAs>CompileAsC</CompileAs>
      <AdditionalIncludeDirectories />
      <PreprocessorDefinitions>_WIN8_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Link>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Vista Release|x64'">
    <ClCompile>
      <CompileAs>CompileAsC</CompileAs>
      <AdditionalIncludeDirectories />
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win7 Debug|x64'">
    <ClCompile>
      <CompileAs>CompileAsC</CompileAs>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories />
      <PreprocessorDefinitions>_WIN7_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Link>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win7 Release|x64'">
    <ClCompile>
      <CompileAs>CompileAsC</CompileAs>
      <AdditionalIncludeDirectories />
      <PreprocessorDefinitions>_WIN7_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Link>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win8.1 Release|x64'">
    <ClCompile>
      <CompileAs>CompileAsC</CompileAs>
      <AdditionalIncludeDirectories>
      </AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WIN81_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win10Release|x64'">
    <ClCompile>
      <CompileAs>CompileAsC</CompileAs>
      <AdditionalIncludeDirectories>
      </AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WIN10_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win8 Release|x64'">
    <ClCompile>
      <CompileAs>CompileAsC</CompileAs>
      <AdditionalIncludeDirectories />
      <PreprocessorDefinitions>_WIN8_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Link>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <FilesToPackage Include="$(TargetPath)" />
    <FilesToPackage Include="@(Inf->'%(CopyOutput)')" Condition="'@(Inf)'!=''" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="BlackBoneDrv.c" />
    <ClCompile Include="Dispatch.c" />
    <ClCompile Include="Inject.c" />
    <ClCompile Include="ldrreloc.c" />
    <ClCompile Include="Loader.c" />
    <ClCompile Include="MMap.c" />
    <ClCompile Include="NotifyRoutine.c" />
    <ClCompile Include="Private.c" />
    <ClCompile Include="Remap.c" />
    <ClCompile Include="Routines.c" />
    <ClCompile Include="Utils.c" />
    <ClCompile Include="VadHelpers.c" />
    <ClCompile Include="VadRoutines.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="BlackBoneDrv.h" />
    <ClInclude Include="BlackBoneDef.h" />
    <ClInclude Include="Utils.h" />
    <ClInclude Include="Imports.h" />
    <ClInclude Include="Loader.h" />
    <ClInclude Include="NativeStructs.h" />
    <ClInclude Include="NativeStructs10.h" />
    <ClInclude Include="NativeStructs7.h" />
    <ClInclude Include="NativeStructs8.h" />
    <ClInclude Include="NativeStructs81.h" />
    <ClInclude Include="NativeEnums.h" />
    <ClInclude Include="PEStructs.h" />
    <ClInclude Include="Remap.h" />
    <ClInclude Include="Private.h" />
    <ClInclude Include="Routines.h" />
    <ClInclude Include="VadHelpers.h" />
    <ClInclude Include="VadRoutines.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>