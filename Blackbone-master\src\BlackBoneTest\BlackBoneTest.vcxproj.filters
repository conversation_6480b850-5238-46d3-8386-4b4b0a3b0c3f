﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="TestAsmVariant.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestBasic.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestDriver.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestLocalHook.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestManualMap.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestPatternScan.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestRemoteCall.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestRemoteHook.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestRemoteMemory.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestSyscall.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestAsmJit.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestMultiPtr.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestGuard.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestSymbols.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
    <ClCompile Include="TestModules.cpp">
      <Filter>Tests</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Common.h" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Tests">
      <UniqueIdentifier>{b5cc080f-5ea5-4f14-b732-c1aaf61443c8}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>