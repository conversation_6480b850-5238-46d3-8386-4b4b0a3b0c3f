// [AsmJit]
// Complete x86/x64 JIT and Remote Assembler for C++.
//
// [License]
// Zlib - See LICENSE.md file in the package.

// [Guard]
#ifndef _ASMJIT_X86_H
#define _ASMJIT_X86_H

// [Dependencies - AsmJit]
#include "base.h"

#include "x86/x86assembler.h"
#include "x86/x86compiler.h"
#include "x86/x86cpuinfo.h"
#include "x86/x86inst.h"
#include "x86/x86operand.h"

// [Guard]
#endif // _ASMJIT_X86_H
